import React from 'react';
import { FacebookIcon, InstagramIcon } from './icons/SocialIcons';
import { WhatsAppIcon } from './icons/WhatsAppIcon';

interface SocialLinkItem {
  name: string;
  Icon: React.ElementType;
  href: string;
  bgColor: string;
  hoverBgColor: string;
  textColor: string;
  ariaLabel: string;
}

const socialLinks: SocialLinkItem[] = [
  {
    name: 'WhatsApp',
    Icon: WhatsAppIcon,
    href: 'https://wa.me/1234567890', // Replace with your WhatsApp number
    bgColor: 'bg-gradient-growth',
    hoverBgColor: 'hover:bg-gradient-energy',
    textColor: 'text-white',
    ariaLabel: 'Chat on WhatsApp',
  },
  {
    name: 'Facebook',
    Icon: FacebookIcon,
    href: 'https://facebook.com/rebelrover', // Replace with your Facebook page
    bgColor: 'bg-gradient-trust',
    hoverBgColor: 'hover:bg-gradient-sky',
    textColor: 'text-white',
    ariaLabel: 'Follow us on Facebook',
  },
  {
    name: 'Instagram',
    Icon: InstagramIcon,
    href: 'https://instagram.com/rebelrover', // Replace with your Instagram page
    bgColor: 'bg-gradient-sunrise',
    hoverBgColor: 'hover:bg-gradient-energy',
    textColor: 'text-white',
    ariaLabel: 'Follow us on Instagram',
  },
];

const FloatingSocialIcons: React.FC = () => {
  return (
    <div className="fixed top-1/2 right-0 transform -translate-y-1/2 z-40 flex flex-col items-end">
      {socialLinks.map((social, index) => (
        <a
          key={social.name}
          href={social.href}
          target="_blank"
          rel="noopener noreferrer"
          className={`
            flex items-center justify-center relative
            h-14 my-1.5 pl-4 pr-3
            ${social.bgColor} ${social.textColor}
            rounded-l-2xl shadow-eco-xl
            transition-all duration-500 ease-out
            group focus:outline-none focus:ring-4 focus:ring-forest-light focus:ring-opacity-50
            w-14 hover:w-48 hover:shadow-eco-xl
            transform hover:scale-105 hover:-translate-x-2
            border-2 border-white/20 hover:border-white/40
            backdrop-blur-sm
            eco-float opacity-energy
          `}
          style={{
            animationDelay: `${index * 0.2}s`,
            background: `linear-gradient(135deg, var(--tw-gradient-stops))`,
          }}
          aria-label={social.ariaLabel}
        >
          {/* Animated background shimmer */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-l-2xl"></div>

          {/* Icon with enhanced styling */}
          <social.Icon className="w-7 h-7 flex-shrink-0 relative z-10 drop-shadow-lg group-hover:scale-110 transition-transform duration-300" />

          {/* Enhanced text with better animation */}
          <span
            className="
              ml-3 text-sm font-semibold relative z-10
              opacity-0 group-hover:opacity-100
              transform translate-x-4 group-hover:translate-x-0
              transition-all duration-300 delay-150
              whitespace-nowrap overflow-hidden
              drop-shadow-sm
            "
          >
            {social.name === 'Instagram' ? '📸 Follow Us' : social.name === 'Facebook' ? '👍 Like Us' : '💬 Chat Now'}
          </span>

          {/* Pulsing indicator dot */}
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-sunrise-gold rounded-full opacity-80 animate-pulse shadow-lg"></div>

          {/* Eco-friendly corner accent */}
          <div className="absolute bottom-1 left-1 w-2 h-2 bg-white/30 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
        </a>
      ))}

      {/* Floating background element */}
      <div className="absolute -right-8 top-1/2 transform -translate-y-1/2 w-32 h-32 bg-forest-light rounded-full blur-3xl opacity-atmosphere-far eco-organic-float pointer-events-none"></div>
    </div>
  );
};

export default FloatingSocialIcons;
