
import React from 'react';

const Subscribe: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-subscribe-pattern bg-cover bg-center relative">
      <div className="absolute inset-0 bg-gradient-to-br from-brand-teal/80 via-brand-teal-dark/70 to-brand-mint/60"></div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4 font-poppins">
          Subscribe to Our Newsletter
        </h2>
        <p className="text-lg text-white/90 mb-8 max-w-xl mx-auto font-montserrat">
          Get the latest travel updates, exclusive deals, and inspiring stories delivered straight to your inbox.
        </p>
        <form className="max-w-lg mx-auto flex flex-col sm:flex-row gap-4">
          <input
            type="email"
            placeholder="Enter your email address"
            className="flex-grow px-6 py-4 rounded-lg text-brand-teal focus:outline-none focus:ring-2 focus:ring-brand-green shadow-eco font-montserrat bg-brand-cream"
            required
          />
          <button
            type="submit"
            className="bg-gradient-secondary text-brand-teal px-8 py-4 rounded-lg font-semibold hover:shadow-eco-xl transition-all duration-300 font-poppins shadow-eco transform hover:scale-105"
          >
            Subscribe
          </button>
        </form>
      </div>
    </section>
  );
};

export default Subscribe;
