
import React from 'react';

const Subscribe: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-subscribe-pattern bg-cover bg-center relative overflow-hidden">
      {/* Enhanced eco-friendly overlay with lower opacity */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-teal/70 via-brand-teal-dark/60 to-brand-mint/50"></div>

      {/* Eco-friendly floating elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-20 w-32 h-32 bg-brand-green rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-brand-mint rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-brand-teal rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4 font-poppins eco-text-shadow">
          🌿 Subscribe to Our Eco-Newsletter
        </h2>
        <p className="text-lg text-white/95 mb-8 max-w-xl mx-auto font-montserrat leading-relaxed">
          Get the latest sustainable travel updates, exclusive eco-friendly deals, and inspiring green stories delivered straight to your inbox.
        </p>
        <form className="max-w-lg mx-auto flex flex-col sm:flex-row gap-4">
          <input
            type="email"
            placeholder="Enter your email for eco-updates"
            className="flex-grow px-6 py-4 rounded-xl text-brand-teal focus:outline-none focus:ring-2 focus:ring-brand-green shadow-eco-lg font-montserrat bg-brand-cream/95 backdrop-blur-sm border-2 border-brand-mint/30 focus:border-brand-green transition-all duration-300"
            required
          />
          <button
            type="submit"
            className="eco-btn-secondary px-8 py-4 rounded-xl font-semibold transition-all duration-300 font-poppins shadow-eco-lg transform hover:scale-105 eco-float"
          >
            🌱 Subscribe
          </button>
        </form>
      </div>
    </section>
  );
};

export default Subscribe;
