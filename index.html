
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rebel Rover</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="index.css">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              poppins: ['Poppins', 'sans-serif'],
              montserrat: ['Montserrat', 'sans-serif'],
            },
            colors: {
              // Color Hunt Palette: https://colorhunt.co/palette/3d8d7ab3d8a8fbffe4a3d1c6
              // Primary Colors
              'brand-teal': '#3D8D7A',          // Deep Teal (primary)
              'brand-teal-dark': '#2F6B5C',     // Darker Teal
              'brand-teal-light': '#4FA089',    // Lighter Teal

              // Secondary Colors
              'brand-green': '#B3D8A8',         // Light Green (secondary)
              'brand-green-dark': '#8FC283',    // Darker Light Green
              'brand-green-light': '#C8E6BD',   // Lighter Green

              // Background Colors
              'brand-cream': '#FBFFE4',         // Cream/Off-white (main background)
              'brand-cream-dark': '#F5F9D3',    // Slightly darker cream

              // Accent Colors
              'brand-mint': '#A3D1C6',          // Mint Green (accent)
              'brand-mint-dark': '#8BC4B5',     // Darker Mint
              'brand-mint-light': '#B8DDD4',    // Lighter Mint

              // Neutral Colors (derived from palette)
              'brand-gray': '#5A6B5D',          // Muted gray-green
              'brand-gray-light': '#F0F4ED',    // Very light gray-green
              'brand-gray-medium': '#D4E0D1',   // Medium gray-green
              'brand-gray-dark': '#3A4A3D',     // Dark gray-green

              // Legacy color mappings for compatibility
              'brand-orange': '#3D8D7A',        // Map to primary teal
              'brand-orange-dark': '#2F6B5C',   // Map to dark teal
              'brand-orange-light': '#4FA089',  // Map to light teal
              'brand-blue-dark': '#3A4A3D',     // Map to dark gray-green
              'brand-blue-light': '#5A6B5D',    // Map to gray-green
              'brand-blue': '#3D8D7A',          // Map to primary teal

              // Eco colors using new palette
              'eco-green': '#B3D8A8',           // Light green
              'eco-green-light': '#C8E6BD',     // Lighter green
              'eco-green-dark': '#8FC283',      // Darker green
              'eco-sage': '#A3D1C6',           // Mint green

              // Status Colors
              'success': '#B3D8A8',             // Success - light green
              'warning': '#F4D03F',             // Warning - warm yellow
              'error': '#E74C3C',               // Error - soft red
              'info': '#3D8D7A',                // Info - primary teal
            },
            backgroundImage: {
              'hero-pattern': "url('https://picsum.photos/seed/nature-forest/1920/1080')",
              'subscribe-pattern': "url('https://picsum.photos/seed/nature-mountains/1600/500')",
              // Color Hunt Palette Gradients
              'gradient-primary': 'linear-gradient(135deg, #3D8D7A 0%, #2F6B5C 100%)',
              'gradient-secondary': 'linear-gradient(135deg, #B3D8A8 0%, #8FC283 100%)',
              'gradient-hero': 'linear-gradient(135deg, #3A4A3D 0%, #3D8D7A 50%, #A3D1C6 100%)',
              'gradient-card': 'linear-gradient(145deg, #FBFFE4 0%, #F5F9D3 100%)',
              'gradient-mint': 'linear-gradient(135deg, #A3D1C6 0%, #8BC4B5 100%)',
              'gradient-cream': 'linear-gradient(135deg, #FBFFE4 0%, #F0F4ED 100%)',
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
</head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
