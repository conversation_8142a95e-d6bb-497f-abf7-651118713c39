import React, { useRef, useState, useEffect } from 'react';
import { ABOUT_GALLERY_IMAGES } from '../constants';
import type { GalleryImage } from '../types';

const AboutGallery: React.FC = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false); // Start with false, will be updated after mount
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Check scroll position to update navigation buttons and indicators
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      const isAtStart = scrollLeft <= 5; // Small tolerance for floating point precision
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 5;

      setCanScrollLeft(!isAtStart);
      setCanScrollRight(!isAtEnd);

      // Calculate scroll progress (0 to 1)
      const maxScroll = scrollWidth - clientWidth;
      const progress = maxScroll > 0 ? scrollLeft / maxScroll : 0;
      setScrollProgress(progress);
    }
  };

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -420, // Scroll by approximately one image width (400px + gap)
        behavior: 'smooth'
      });
      // Check scroll position after animation completes
      setTimeout(checkScrollPosition, 300);
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 420, // Scroll by approximately one image width (400px + gap)
        behavior: 'smooth'
      });
      // Check scroll position after animation completes
      setTimeout(checkScrollPosition, 300);
    }
  };

  // Handle image click for lightbox
  const openLightbox = (imageSrc: string) => {
    setSelectedImage(imageSrc);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeLightbox();
      }
    };

    if (selectedImage) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);

  // Check scroll position on mount and resize
  useEffect(() => {
    // Use a timeout to ensure images are loaded and container has proper dimensions
    const timer = setTimeout(() => {
      checkScrollPosition();
    }, 100);

    const handleResize = () => checkScrollPosition();
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <section className="py-16 lg:py-24 bg-brand-gray-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">Gallery</h2>
          <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
            Explore our journey and the beautiful moments captured along the way.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Arrows */}
          <button
            onClick={scrollLeft}
            disabled={!canScrollLeft}
            style={{
              position: 'absolute',
              left: '8px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 50,
              cursor: 'pointer',
              pointerEvents: 'auto'
            }}
            className={`w-14 h-14 rounded-full bg-white eco-shadow-lg flex items-center justify-center transition-all duration-300 border-2 ${
              canScrollLeft
                ? 'text-brand-blue-dark hover:bg-brand-orange hover:text-white hover:border-brand-orange border-eco-green-light'
                : 'text-gray-400 cursor-not-allowed border-brand-gray-light opacity-50'
            }`}
            aria-label="Scroll left"
          >
            <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={scrollRight}
            disabled={!canScrollRight}
            style={{
              position: 'absolute',
              right: '8px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 50,
              cursor: 'pointer',
              pointerEvents: 'auto'
            }}
            className={`w-14 h-14 rounded-full bg-white eco-shadow-lg flex items-center justify-center transition-all duration-300 border-2 ${
              canScrollRight
                ? 'text-brand-blue-dark hover:bg-brand-orange hover:text-white hover:border-brand-orange border-eco-green-light'
                : 'text-gray-400 cursor-not-allowed border-brand-gray-light opacity-50'
            }`}
            aria-label="Scroll right"
          >
            <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Scrollable Gallery */}
          <div
            ref={scrollContainerRef}
            onScroll={checkScrollPosition}
            className="flex gap-6 overflow-x-auto pb-4 px-20 scrollbar-hide"
            style={{
              scrollbarWidth: 'none', // Firefox
              msOverflowStyle: 'none', // IE/Edge
            }}
          >
            {ABOUT_GALLERY_IMAGES.map((image: GalleryImage) => (
              <div
                key={image.id}
                className="flex-shrink-0 w-96 h-96 overflow-hidden rounded-xl shadow-lg group cursor-pointer relative gallery-item"
                onClick={() => openLightbox(image.src)}
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Progress Bar */}
          <div className="flex justify-center mt-6">
            <div className="w-64 h-1 bg-brand-gray-light rounded-full overflow-hidden">
              <div
                className="h-full eco-gradient-primary transition-all duration-300 ease-out"
                style={{ width: `${scrollProgress * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Lightbox Modal */}
        {selectedImage && (
          <div
            className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4 lightbox-backdrop"
            onClick={closeLightbox}
          >
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={closeLightbox}
                className="absolute -top-12 right-0 text-white hover:text-brand-orange transition-colors duration-300"
                aria-label="Close lightbox"
              >
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              <img
                src={selectedImage}
                alt="Gallery image"
                className="max-w-full max-h-full object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default AboutGallery;
