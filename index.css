/* Eco-friendly styles for the Rebel Rover travel website */

/* Eco-friendly gradient backgrounds */
.eco-gradient-primary {
  background: linear-gradient(135deg, #8FBC8F 0%, #6B8E6B 100%);
}

.eco-gradient-secondary {
  background: linear-gradient(135deg, #228B22 0%, #556B2F 100%);
}

.eco-gradient-earth {
  background: linear-gradient(135deg, #8B4513 0%, #D2B48C 100%);
}

.eco-gradient-sky {
  background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
}

/* Nature-inspired text shadows */
.eco-text-shadow {
  text-shadow: 2px 2px 4px rgba(47, 79, 47, 0.3);
}

/* Eco-friendly box shadows */
.eco-shadow {
  box-shadow: 0 4px 6px -1px rgba(47, 79, 47, 0.1), 0 2px 4px -1px rgba(47, 79, 47, 0.06);
}

.eco-shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(47, 79, 47, 0.1), 0 4px 6px -2px rgba(47, 79, 47, 0.05);
}

/* Hide scrollbar for webkit browsers (Chrome, Safari, Edge) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.scrollbar-hide {
  scrollbar-width: none;
}

/* Hide scrollbar for IE/Edge */
.scrollbar-hide {
  -ms-overflow-style: none;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Eco-friendly focus styles for better accessibility */
.focus-visible:focus {
  outline: 2px solid #8FBC8F;
  outline-offset: 2px;
}

/* Custom animation for gallery images */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Custom hover effects for gallery items */
.gallery-item {
  transition: all 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Auto-scroll destination cards animations */
.destination-card {
  transition: all 0.3s ease;
}

.destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 30px -5px rgba(47, 79, 47, 0.15), 0 15px 15px -5px rgba(47, 79, 47, 0.08);
}

/* Auto-scroll container smooth scrolling */
.auto-scroll-container {
  scroll-behavior: smooth;
  transition: scroll-left 0.5s ease-in-out;
}

/* Auto-scroll pause indicator animation */
@keyframes pulse-eco {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.auto-scroll-indicator {
  animation: pulse-eco 2s infinite;
}

/* Destination card entrance animation */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.destination-card-enter {
  animation: slideInFromRight 0.6s ease-out;
}

/* Ensure navigation buttons are always visible and clickable */
.gallery-nav-button {
  pointer-events: auto !important;
  z-index: 30 !important;
  position: absolute !important;
  cursor: pointer !important;
}

.gallery-nav-button:hover {
  transform: scale(1.05);
}

.gallery-nav-button:active {
  transform: scale(0.95);
}

/* Lightbox backdrop blur effect */
.lightbox-backdrop {
  backdrop-filter: blur(8px);
}

/* Color Hunt Palette Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #FBFFE4;
}

::-webkit-scrollbar-thumb {
  background: #3D8D7A;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2F6B5C;
}

/* Line clamp utilities for blog cards */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Blog card animations with Color Hunt Palette */
.blog-card {
  transition: all 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(61, 141, 122, 0.15), 0 10px 10px -5px rgba(61, 141, 122, 0.08);
}

/* Color Hunt Palette Utilities */
.text-gradient-primary {
  background: linear-gradient(135deg, #3D8D7A 0%, #2F6B5C 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, #B3D8A8 0%, #8FC283 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #3D8D7A 0%, #2F6B5C 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #B3D8A8 0%, #8FC283 100%);
}

.bg-gradient-hero {
  background: linear-gradient(135deg, #3A4A3D 0%, #3D8D7A 50%, #A3D1C6 100%);
}

.bg-gradient-card {
  background: linear-gradient(145deg, #FBFFE4 0%, #F5F9D3 100%);
}

.bg-gradient-mint {
  background: linear-gradient(135deg, #A3D1C6 0%, #8BC4B5 100%);
}

.bg-gradient-cream {
  background: linear-gradient(135deg, #FBFFE4 0%, #F0F4ED 100%);
}

/* Enhanced shadow utilities with Color Hunt Palette */
.shadow-eco {
  box-shadow: 0 4px 6px -1px rgba(61, 141, 122, 0.1), 0 2px 4px -1px rgba(61, 141, 122, 0.06);
}

.shadow-eco-lg {
  box-shadow: 0 10px 15px -3px rgba(61, 141, 122, 0.1), 0 4px 6px -2px rgba(61, 141, 122, 0.05);
}

.shadow-eco-xl {
  box-shadow: 0 20px 25px -5px rgba(61, 141, 122, 0.1), 0 10px 10px -5px rgba(61, 141, 122, 0.04);
}

/* Color Hunt Palette specific shadows */
.shadow-teal {
  box-shadow: 0 4px 6px -1px rgba(61, 141, 122, 0.15), 0 2px 4px -1px rgba(61, 141, 122, 0.08);
}

.shadow-mint {
  box-shadow: 0 4px 6px -1px rgba(163, 209, 198, 0.15), 0 2px 4px -1px rgba(163, 209, 198, 0.08);
}

/* Video Background Optimizations */
.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

/* Ensure video covers the entire hero section */
#home video {
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Enhanced Gallery Animations */
.gallery-item {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Eco-friendly pulse animation */
@keyframes eco-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.eco-pulse {
  animation: eco-pulse 2s infinite;
}

/* Enhanced backdrop blur for modern browsers */
.enhanced-backdrop {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}

/* Smooth border transitions */
.border-3 {
  border-width: 3px;
}

/* Eco-friendly glow effect */
.eco-glow {
  box-shadow: 0 0 20px rgba(61, 141, 122, 0.3), 0 0 40px rgba(61, 141, 122, 0.1);
}

.eco-glow-hover:hover {
  box-shadow: 0 0 30px rgba(61, 141, 122, 0.4), 0 0 60px rgba(61, 141, 122, 0.2);
}

/* 🌿 ADVANCED ECO-PSYCHOLOGY CARD STYLES */
/* Based on biophilic design and emotional color theory */

.eco-card {
  background: linear-gradient(145deg, #FEFAE0 0%, #FAEDCD 100%);
  border: 1px solid rgba(168, 218, 220, 0.25);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.eco-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(116, 198, 157, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(168, 218, 220, 0.06) 0%, transparent 50%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.eco-card:hover {
  background: linear-gradient(145deg, #FAEDCD 0%, #FEFAE0 100%);
  border-color: rgba(61, 141, 122, 0.4);
  transform: translateY(-12px) scale(1.03);
  box-shadow:
    0 25px 50px rgba(27, 67, 50, 0.15),
    0 15px 30px rgba(116, 198, 157, 0.1),
    0 5px 15px rgba(168, 218, 220, 0.08);
}

.eco-card:hover::before {
  opacity: 1;
}

/* Trust-building card variant */
.eco-card-trust {
  background: linear-gradient(145deg, #E9EDC9 0%, #CCD5AE 100%);
  border: 1px solid rgba(45, 90, 61, 0.2);
}

.eco-card-trust:hover {
  background: linear-gradient(145deg, #CCD5AE 0%, #E9EDC9 100%);
  border-color: rgba(45, 90, 61, 0.4);
  box-shadow:
    0 25px 50px rgba(27, 67, 50, 0.2),
    0 15px 30px rgba(45, 90, 61, 0.12);
}

/* Growth-inspiring card variant */
.eco-card-growth {
  background: linear-gradient(145deg, #D8F3DC 0%, #B7E4C7 100%);
  border: 1px solid rgba(82, 183, 136, 0.25);
}

.eco-card-growth:hover {
  background: linear-gradient(145deg, #B7E4C7 0%, #D8F3DC 100%);
  border-color: rgba(82, 183, 136, 0.4);
  box-shadow:
    0 25px 50px rgba(82, 183, 136, 0.15),
    0 15px 30px rgba(116, 198, 157, 0.1);
}

/* 🌿 ADVANCED ECO-PSYCHOLOGY BUTTON STYLES */
/* Designed to trigger positive emotional responses and trust */

.eco-btn-primary {
  background: linear-gradient(135deg, #3D8D7A 0%, #2D5A3D 100%);
  color: white;
  border: 2px solid transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.eco-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.eco-btn-primary:hover {
  background: linear-gradient(135deg, #52B788 0%, #3D8D7A 100%);
  border-color: rgba(183, 228, 199, 0.6);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(27, 67, 50, 0.25),
    0 8px 20px rgba(61, 141, 122, 0.2),
    0 3px 10px rgba(82, 183, 136, 0.15);
}

.eco-btn-primary:hover::before {
  left: 100%;
}

.eco-btn-secondary {
  background: linear-gradient(135deg, #B7E4C7 0%, #95D5B2 100%);
  color: #1B4332;
  border: 2px solid transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.eco-btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.eco-btn-secondary:hover {
  background: linear-gradient(135deg, #74C69D 0%, #B7E4C7 100%);
  border-color: rgba(27, 67, 50, 0.3);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(116, 198, 157, 0.25),
    0 8px 20px rgba(183, 228, 199, 0.2),
    0 3px 10px rgba(149, 213, 178, 0.15);
}

.eco-btn-secondary:hover::before {
  left: 100%;
}

/* Action-oriented button for CTAs */
.eco-btn-action {
  background: linear-gradient(135deg, #F8961E 0%, #F9C74F 100%);
  color: #1B4332;
  border: 2px solid transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-weight: 700;
  letter-spacing: 0.05em;
}

.eco-btn-action:hover {
  background: linear-gradient(135deg, #F3722C 0%, #F8961E 100%);
  border-color: rgba(249, 199, 79, 0.6);
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 15px 35px rgba(248, 150, 30, 0.3),
    0 8px 20px rgba(249, 199, 79, 0.25),
    0 3px 10px rgba(243, 114, 44, 0.2);
}

/* Trust-building button variant */
.eco-btn-trust {
  background: linear-gradient(135deg, #4A90A4 0%, #2C5F7C 100%);
  color: white;
  border: 2px solid transparent;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.eco-btn-trust:hover {
  background: linear-gradient(135deg, #7FB3D3 0%, #4A90A4 100%);
  border-color: rgba(168, 218, 220, 0.5);
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(44, 95, 124, 0.25),
    0 8px 20px rgba(74, 144, 164, 0.2);
}

/* 🌿 ADVANCED ECO-PSYCHOLOGY SECTION BACKGROUNDS */
/* Designed to create emotional depth and biophilic connection */

.eco-section-light {
  background: linear-gradient(135deg, #FEFAE0 0%, #E9EDC9 100%);
  position: relative;
  overflow: hidden;
}

.eco-section-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 75%, rgba(116, 198, 157, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 75% 25%, rgba(168, 218, 220, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, rgba(183, 228, 199, 0.04) 0%, transparent 70%);
  pointer-events: none;
  animation: eco-breathe 8s ease-in-out infinite;
}

.eco-section-dark {
  background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
  position: relative;
  overflow: hidden;
}

.eco-section-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 70%, rgba(61, 141, 122, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 70% 30%, rgba(168, 218, 220, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 50% 20%, rgba(82, 183, 136, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: eco-breathe 10s ease-in-out infinite reverse;
}

/* Trust-building section */
.eco-section-trust {
  background: linear-gradient(135deg, #E9EDC9 0%, #CCD5AE 100%);
  position: relative;
}

.eco-section-trust::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 40% 60%, rgba(45, 90, 61, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 60% 40%, rgba(61, 141, 122, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

/* Growth-inspiring section */
.eco-section-growth {
  background: linear-gradient(135deg, #D8F3DC 0%, #B7E4C7 100%);
  position: relative;
}

.eco-section-growth::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 35% 65%, rgba(82, 183, 136, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 65% 35%, rgba(116, 198, 157, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

/* 🌿 ADVANCED ECO-PSYCHOLOGY TEXT GRADIENTS */
/* Designed to evoke specific emotional responses */

.text-eco-primary {
  background: linear-gradient(135deg, #3D8D7A 0%, #1B4332 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.text-eco-secondary {
  background: linear-gradient(135deg, #B7E4C7 0%, #74C69D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Trust-building text */
.text-eco-trust {
  background: linear-gradient(135deg, #2D5A3D 0%, #4A90A4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Growth-inspiring text */
.text-eco-growth {
  background: linear-gradient(135deg, #52B788 0%, #74C69D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Energy and action text */
.text-eco-energy {
  background: linear-gradient(135deg, #F8961E 0%, #F9C74F 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Harmony and peace text */
.text-eco-harmony {
  background: linear-gradient(135deg, #95D5B2 0%, #A8DADC 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}

/* Enhanced text shadow for better readability */
.eco-text-shadow {
  text-shadow:
    0 2px 4px rgba(27, 67, 50, 0.3),
    0 1px 2px rgba(27, 67, 50, 0.2);
}

/* 🎭 ADVANCED OPACITY PSYCHOLOGY SYSTEM */
/* Based on visual perception research and accessibility principles */

/* === READABILITY OPACITY LEVELS === */
/* Primary content - Maximum readability */
.opacity-content-primary { opacity: 1; }           /* 100% - Main text, headings, CTAs */
.opacity-content-secondary { opacity: 0.95; }      /* 95% - Secondary text, descriptions */
.opacity-content-tertiary { opacity: 0.87; }       /* 87% - Captions, metadata, timestamps */
.opacity-content-disabled { opacity: 0.6; }        /* 60% - Disabled states, placeholders */

/* === BACKGROUND OPACITY LEVELS === */
/* Overlay backgrounds - Balanced visibility */
.opacity-overlay-light { opacity: 0.15; }          /* 15% - Subtle background patterns */
.opacity-overlay-medium { opacity: 0.25; }         /* 25% - Decorative elements */
.opacity-overlay-strong { opacity: 0.4; }          /* 40% - Important overlays */
.opacity-overlay-dominant { opacity: 0.65; }       /* 65% - Hero overlays, modals */

/* === INTERACTIVE OPACITY LEVELS === */
/* Hover and focus states */
.opacity-hover-subtle { opacity: 0.8; }            /* 80% - Subtle hover feedback */
.opacity-hover-medium { opacity: 0.9; }            /* 90% - Standard hover state */
.opacity-hover-strong { opacity: 1; }              /* 100% - Active/focused state */

/* === ATMOSPHERIC OPACITY LEVELS === */
/* Creating depth and atmosphere */
.opacity-atmosphere-far { opacity: 0.08; }         /* 8% - Far background elements */
.opacity-atmosphere-mid { opacity: 0.12; }         /* 12% - Mid-ground elements */
.opacity-atmosphere-near { opacity: 0.18; }        /* 18% - Near background elements */

/* === BRAND OPACITY LEVELS === */
/* Eco-friendly specific opacities */
.opacity-eco-whisper { opacity: 0.06; }            /* 6% - Barely visible eco accents */
.opacity-eco-breath { opacity: 0.1; }              /* 10% - Breathing background elements */
.opacity-eco-presence { opacity: 0.16; }           /* 16% - Noticeable but subtle */
.opacity-eco-statement { opacity: 0.3; }           /* 30% - Clear eco messaging */

/* === ACCESSIBILITY OPACITY LEVELS === */
/* WCAG compliant opacity levels */
.opacity-accessible-light { opacity: 0.7; }        /* 70% - Minimum for secondary text */
.opacity-accessible-medium { opacity: 0.85; }      /* 85% - Good for most content */
.opacity-accessible-high { opacity: 0.95; }        /* 95% - Excellent readability */

/* === DYNAMIC OPACITY CLASSES === */
/* Responsive opacity based on context */
.opacity-responsive-mobile { opacity: 0.9; }       /* 90% - Mobile optimization */
.opacity-responsive-tablet { opacity: 0.93; }      /* 93% - Tablet optimization */
.opacity-responsive-desktop { opacity: 0.95; }     /* 95% - Desktop optimization */

/* === EMOTIONAL OPACITY LEVELS === */
/* Psychology-based opacity for emotional impact */
.opacity-trust { opacity: 0.92; }                  /* 92% - Builds trust and reliability */
.opacity-energy { opacity: 0.98; }                 /* 98% - High energy, action-oriented */
.opacity-calm { opacity: 0.85; }                   /* 85% - Peaceful, meditative */
.opacity-mystery { opacity: 0.75; }                /* 75% - Intriguing, sophisticated */

/* 🌿 ADVANCED ECO-PSYCHOLOGY ANIMATIONS */
/* Designed to mimic natural movements and create emotional connection */

@keyframes eco-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-8px) rotate(1deg);
  }
  66% {
    transform: translateY(-12px) rotate(-1deg);
  }
}

.eco-float {
  animation: eco-float 4s ease-in-out infinite;
}

@keyframes eco-breathe {
  0%, 100% {
    opacity: 0.1;  /* opacity-eco-breath */
    transform: scale(1);
  }
  50% {
    opacity: 0.16; /* opacity-eco-presence */
    transform: scale(1.05);
  }
}

.eco-breathe {
  animation: eco-breathe 6s ease-in-out infinite;
}

@keyframes eco-shimmer {
  0% {
    background-position: -200% 0;
    opacity: 0;
  }
  50% {
    opacity: 0.3; /* opacity-eco-statement */
  }
  100% {
    background-position: 200% 0;
    opacity: 0;
  }
}

.eco-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: eco-shimmer 3s ease-in-out infinite;
}

@keyframes eco-pulse-gentle {
  0%, 100% {
    opacity: 0.06; /* opacity-eco-whisper */
    transform: scale(1);
  }
  50% {
    opacity: 0.16; /* opacity-eco-presence */
    transform: scale(1.1);
  }
}

.eco-pulse-gentle {
  animation: eco-pulse-gentle 4s ease-in-out infinite;
}

@keyframes eco-wave {
  0%, 100% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(5px) translateY(-3px);
  }
  50% {
    transform: translateX(0) translateY(-5px);
  }
  75% {
    transform: translateX(-5px) translateY(-3px);
  }
}

.eco-wave {
  animation: eco-wave 8s ease-in-out infinite;
}

@keyframes eco-grow {
  0% {
    transform: scale(0.95);
    opacity: 0.85; /* opacity-calm */
  }
  50% {
    transform: scale(1.02);
    opacity: 0.98; /* opacity-energy */
  }
  100% {
    transform: scale(0.95);
    opacity: 0.85; /* opacity-calm */
  }
}

.eco-grow {
  animation: eco-grow 5s ease-in-out infinite;
}

/* === ADVANCED OPACITY-BASED ANIMATIONS === */

@keyframes eco-fade-in-trust {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 0.92; /* opacity-trust */
    transform: translateY(0);
  }
}

.eco-fade-in-trust {
  animation: eco-fade-in-trust 0.8s ease-out forwards;
}

@keyframes eco-fade-in-energy {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 0.98; /* opacity-energy */
    transform: scale(1);
  }
}

.eco-fade-in-energy {
  animation: eco-fade-in-energy 0.6s ease-out forwards;
}

@keyframes eco-fade-in-calm {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 0.85; /* opacity-calm */
    transform: translateX(0);
  }
}

.eco-fade-in-calm {
  animation: eco-fade-in-calm 1s ease-out forwards;
}

@keyframes eco-opacity-wave {
  0%, 100% {
    opacity: 0.08; /* opacity-atmosphere-far */
  }
  25% {
    opacity: 0.12; /* opacity-atmosphere-mid */
  }
  50% {
    opacity: 0.18; /* opacity-atmosphere-near */
  }
  75% {
    opacity: 0.12; /* opacity-atmosphere-mid */
  }
}

.eco-opacity-wave {
  animation: eco-opacity-wave 8s ease-in-out infinite;
}

/* Organic movement for background elements */
@keyframes eco-organic-float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(10px, -15px) rotate(2deg);
  }
  50% {
    transform: translate(-5px, -25px) rotate(-1deg);
  }
  75% {
    transform: translate(-15px, -10px) rotate(1deg);
  }
}

.eco-organic-float {
  animation: eco-organic-float 12s ease-in-out infinite;
}

/* Performance optimization for video */
@media (prefers-reduced-motion: reduce) {
  #home video {
    display: none;
  }

  #home {
    background-image: url('https://picsum.photos/seed/ella-9-arch-bridge/1920/1080');
    background-size: cover;
    background-position: center;
  }
}

/* Mobile optimization - temporarily disabled for testing */
/* @media (max-width: 768px) {
  #home video {
    display: none;
  }

  #home {
    background-image: url('https://picsum.photos/seed/ella-9-arch-bridge/1920/1080');
    background-size: cover;
    background-position: center;
  }
} */
