
import React from 'react';
import { FacebookIcon, InstagramIcon, TwitterIcon, LinkedinIcon } from './icons/SocialIcons';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    company: [
      { label: 'About Us', href: '#' },
      { label: 'Our Team', href: '#' },
      { label: 'Careers', href: '#' },
      { label: 'Blog', href: '#' },
    ],
    support: [
      { label: 'FAQ', href: '#' },
      { label: 'Help Center', href: '#' },
      { label: 'Contact Us', href: '#' },
      { label: 'Terms of Service', href: '#' },
    ],
    destinations: [
      { label: 'Europe', href: '#' },
      { label: 'Asia', href: '#' },
      { label: 'North America', href: '#' },
      { label: 'South America', href: '#' },
    ],
  };

  return (
    <footer id="contact" className="bg-brand-blue-dark text-gray-300 py-16 lg:py-20 font-montserrat">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Column 1: Logo and About */}
          <div>
            <a href="#home" className="text-3xl font-bold text-white font-poppins mb-4 inline-block">
              Rebel <span className="text-brand-orange">Rover</span>
            </a>
            <p className="text-sm leading-relaxed mb-6">
              Your ultimate partner for exploring the world. We provide exceptional travel experiences tailored to your dreams.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-brand-orange transition-colors"><FacebookIcon className="w-6 h-6" /></a>
              <a href="#" className="text-gray-400 hover:text-brand-orange transition-colors"><InstagramIcon className="w-6 h-6" /></a>
              <a href="#" className="text-gray-400 hover:text-brand-orange transition-colors"><TwitterIcon className="w-6 h-6" /></a>
              <a href="#" className="text-gray-400 hover:text-brand-orange transition-colors"><LinkedinIcon className="w-6 h-6" /></a>
            </div>
          </div>

          {/* Column 2: Company Links */}
          <div>
            <h5 className="text-xl font-semibold text-white font-poppins mb-6">Company</h5>
            <ul className="space-y-3">
              {footerLinks.company.map(link => (
                <li key={link.label}><a href={link.href} className="hover:text-brand-orange transition-colors">{link.label}</a></li>
              ))}
            </ul>
          </div>

          {/* Column 3: Support Links */}
          <div>
            <h5 className="text-xl font-semibold text-white font-poppins mb-6">Support</h5>
            <ul className="space-y-3">
              {footerLinks.support.map(link => (
                <li key={link.label}><a href={link.href} className="hover:text-brand-orange transition-colors">{link.label}</a></li>
              ))}
            </ul>
          </div>
          
          {/* Column 4: Top Destinations (or contact info) */}
           <div>
            <h5 className="text-xl font-semibold text-white font-poppins mb-6">Contact Info</h5>
            <ul className="space-y-3">
                <li><span className="font-semibold">Address:</span> 123 Travel Lane, Wanderlust City, TX 78701</li>
                <li><span className="font-semibold">Phone:</span> (*************</li>
                <li><span className="font-semibold">Email:</span> <EMAIL></li>
            </ul>
          </div>

        </div>

        <div className="border-t border-gray-700 pt-8 text-center text-sm">
          <p>&copy; {currentYear} Rebel Rover. All Rights Reserved. Crafted with passion.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
    