
import React, { useEffect, useRef } from 'react';

const Hero: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      // Force video to play
      const playVideo = async () => {
        try {
          video.currentTime = 0;
          await video.play();
          console.log('Video is playing successfully');
        } catch (error) {
          console.log('Video autoplay failed:', error);
          console.log('Error details:', error);
        }
      };

      // Try to play video when component mounts
      const timer = setTimeout(() => {
        playVideo();
      }, 100);

      // Handle video events
      const handleError = (e: any) => {
        console.log('Video failed to load:', e);
      };

      const handleLoadedData = () => {
        console.log('Video loaded successfully');
        playVideo();
      };

      const handleCanPlay = () => {
        console.log('Video can play');
        playVideo();
      };

      video.addEventListener('error', handleError);
      video.addEventListener('loadeddata', handleLoadedData);
      video.addEventListener('canplay', handleCanPlay);

      // Add click handler to play video on user interaction
      const handleClick = () => {
        playVideo();
      };

      document.addEventListener('click', handleClick, { once: true });

      return () => {
        clearTimeout(timer);
        video.removeEventListener('error', handleError);
        video.removeEventListener('loadeddata', handleLoadedData);
        video.removeEventListener('canplay', handleCanPlay);
        document.removeEventListener('click', handleClick);
      };
    }
  }, []);

  return (
    <section id="home" className="relative min-h-screen flex flex-col justify-center items-center text-white pt-32 pb-12 md:pt-40 md:pb-20 overflow-hidden">
      {/* Video Background */}
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover z-0"
        autoPlay
        muted
        loop
        playsInline
        preload="auto"
        poster="https://picsum.photos/seed/ella-9-arch-bridge/1920/1080"
        onError={(e) => console.log('Video error:', e)}
        onLoadStart={() => console.log('Video loading started')}
        onCanPlay={() => console.log('Video can play')}
      >
        {/* Working video sources */}
        <source
          src="https://www.w3schools.com/html/mov_bbb.mp4"
          type="video/mp4"
        />
        <source
          src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4"
          type="video/mp4"
        />
        {/* Fallback message */}
        Your browser does not support the video tag.
      </video>

      {/* Fallback Background Image */}
      <div className="absolute inset-0 bg-hero-pattern bg-cover bg-center z-0"></div>

      {/* Advanced Eco-Psychology Overlay - Lower Opacity for Better Readability */}
      <div className="absolute inset-0 bg-gradient-overlay-forest z-1"></div>

      {/* Sophisticated Eco Pattern Overlay with Advanced Opacity Psychology */}
      <div className="absolute inset-0 z-2">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-forest-light rounded-full blur-3xl eco-organic-float opacity-atmosphere-far"></div>
        <div className="absolute bottom-1/3 right-1/3 w-40 h-40 bg-water-surface rounded-full blur-3xl eco-organic-float opacity-atmosphere-mid" style={{ animationDelay: '4s' }}></div>
        <div className="absolute top-2/3 left-1/2 w-24 h-24 bg-meadow-spring rounded-full blur-2xl eco-organic-float opacity-atmosphere-near" style={{ animationDelay: '8s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-28 h-28 bg-forest-moss rounded-full blur-2xl eco-organic-float opacity-eco-breath" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-36 h-36 bg-sunrise-gold rounded-full blur-3xl eco-opacity-wave" style={{ animationDelay: '6s' }}></div>
      </div>

      {/* Content with Psychology-Based Opacity */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-poppins mb-4 md:mb-6 leading-tight text-white eco-text-shadow opacity-content-primary eco-fade-in-energy">
          EXPLORE NATURE WITH <span className="text-eco-energy eco-shimmer">REBEL ROVER</span>
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl font-montserrat mb-8 md:mb-12 max-w-3xl mx-auto text-white leading-relaxed opacity-content-secondary eco-fade-in-trust">
          Discover sustainable travel experiences that connect you with nature while preserving our planet for future generations through transformative eco-conscious adventures.
        </p>
        <button className="eco-btn-action px-12 py-5 rounded-xl text-lg font-semibold transition-all duration-300 transform shadow-eco-lg eco-float opacity-energy">
          🌿 Explore Eco-Adventures
        </button>
      </div>
    </section>
  );
};

export default Hero;
