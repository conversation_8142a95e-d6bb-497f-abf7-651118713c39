
import React, { useEffect, useRef } from 'react';

const Hero: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      // Force video to play
      const playVideo = async () => {
        try {
          video.currentTime = 0;
          await video.play();
          console.log('Video is playing successfully');
        } catch (error) {
          console.log('Video autoplay failed:', error);
          console.log('Error details:', error);
        }
      };

      // Try to play video when component mounts
      const timer = setTimeout(() => {
        playVideo();
      }, 100);

      // Handle video events
      const handleError = (e: any) => {
        console.log('Video failed to load:', e);
      };

      const handleLoadedData = () => {
        console.log('Video loaded successfully');
        playVideo();
      };

      const handleCanPlay = () => {
        console.log('Video can play');
        playVideo();
      };

      video.addEventListener('error', handleError);
      video.addEventListener('loadeddata', handleLoadedData);
      video.addEventListener('canplay', handleCanPlay);

      // Add click handler to play video on user interaction
      const handleClick = () => {
        playVideo();
      };

      document.addEventListener('click', handleClick, { once: true });

      return () => {
        clearTimeout(timer);
        video.removeEventListener('error', handleError);
        video.removeEventListener('loadeddata', handleLoadedData);
        video.removeEventListener('canplay', handleCanPlay);
        document.removeEventListener('click', handleClick);
      };
    }
  }, []);

  return (
    <section id="home" className="relative min-h-screen flex flex-col justify-center items-center text-white pt-32 pb-12 md:pt-40 md:pb-20 overflow-hidden">
      {/* Video Background */}
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover z-0"
        autoPlay
        muted
        loop
        playsInline
        preload="auto"
        poster="https://picsum.photos/seed/ella-9-arch-bridge/1920/1080"
        onError={(e) => console.log('Video error:', e)}
        onLoadStart={() => console.log('Video loading started')}
        onCanPlay={() => console.log('Video can play')}
      >
        {/* Working video sources */}
        <source
          src="https://www.w3schools.com/html/mov_bbb.mp4"
          type="video/mp4"
        />
        <source
          src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4"
          type="video/mp4"
        />
        {/* Fallback message */}
        Your browser does not support the video tag.
      </video>

      {/* Fallback Background Image */}
      <div className="absolute inset-0 bg-hero-pattern bg-cover bg-center z-0"></div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-gray-dark/40 via-brand-teal/30 to-brand-mint/25 z-1"></div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-poppins mb-4 md:mb-6 leading-tight">
          EXPLORE NATURE WITH <span className="text-brand-green">REBEL ROVER</span>
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl font-montserrat mb-8 md:mb-12 max-w-3xl mx-auto opacity-95">
          Discover sustainable travel experiences that connect you with nature while preserving our planet for future generations.
        </p>
        <button className="bg-gradient-primary text-white px-10 py-4 rounded-lg text-lg font-semibold hover:shadow-eco-xl transition-all duration-300 transform hover:scale-105 shadow-teal">
          Explore Now
        </button>
      </div>
    </section>
  );
};

export default Hero;
