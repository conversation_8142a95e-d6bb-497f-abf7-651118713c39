
import React, { useEffect, useState } from 'react';

// High-quality nature images for the slideshow
const HERO_IMAGES = [
  {
    id: 1,
    url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80',
    alt: 'Misty forest path with sunlight filtering through trees'
  },
  {
    id: 2,
    url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Serene mountain landscape with crystal clear lake'
  },
  {
    id: 3,
    url: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80',
    alt: 'Pristine wilderness with flowing river and green meadows'
  },
  {
    id: 4,
    url: 'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Majestic mountain peaks with golden sunrise'
  },
  {
    id: 5,
    url: 'https://images.unsplash.com/photo-1518837695005-2083093ee35b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    alt: 'Lush green forest canopy with natural light'
  }
];

const Hero: React.FC = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Preload all images for smooth transitions
    const preloadImages = () => {
      HERO_IMAGES.forEach((image) => {
        const img = new Image();
        img.src = image.url;
      });
      setIsLoaded(true);
    };

    preloadImages();

    // Auto-slide images every 6 seconds
    const slideInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === HERO_IMAGES.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(slideInterval);
  }, []);

  return (
    <section id="home" className="relative min-h-screen flex flex-col justify-center items-center text-white pt-32 pb-12 md:pt-40 md:pb-20 overflow-hidden">
      {/* Image Slideshow Background */}
      <div className="absolute inset-0 z-0">
        {HERO_IMAGES.map((image, index) => (
          <div
            key={image.id}
            className={`absolute inset-0 hero-slide-transition ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image.url}
              alt={image.alt}
              className={`w-full h-full object-cover ${
                index === currentImageIndex ? 'ken-burns-effect' : ''
              }`}
              loading={index === 0 ? 'eager' : 'lazy'}
            />
          </div>
        ))}
      </div>

      {/* Minimal overlay for text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-black/20 to-black/30 z-1"></div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-3">
        {HERO_IMAGES.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentImageIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentImageIndex
                ? 'bg-white scale-125 slide-indicator-active'
                : 'bg-white/50 hover:bg-white/75'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Content with Psychology-Based Opacity */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold font-poppins mb-4 md:mb-6 leading-tight text-white eco-text-shadow opacity-content-primary eco-fade-in-energy">
          EXPLORE NATURE WITH <span className="text-eco-energy eco-shimmer">REBEL ROVER</span>
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl font-montserrat mb-8 md:mb-12 max-w-3xl mx-auto text-white leading-relaxed opacity-content-secondary eco-fade-in-trust">
          Discover sustainable travel experiences that connect you with nature while preserving our planet for future generations through transformative eco-conscious adventures.
        </p>
        <button className="eco-btn-action px-12 py-5 rounded-xl text-lg font-semibold transition-all duration-300 transform shadow-eco-lg eco-float opacity-energy">
          🌿 Explore Eco-Adventures
        </button>
      </div>
    </section>
  );
};

export default Hero;
