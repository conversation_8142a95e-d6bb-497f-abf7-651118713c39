import React from 'react';
import type { BlogPost } from '../types';

interface BlogCardProps {
  post: BlogPost;
  onReadMore?: (postId: string) => void;
  featured?: boolean;
}

const BlogCard: React.FC<BlogCardProps> = ({ post, onReadMore, featured = false }) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleReadMore = () => {
    if (onReadMore) {
      onReadMore(post.id);
    } else {
      // Fallback behavior - could scroll to top or show alert
      console.log(`Reading post: ${post.title}`);
    }
  };

  return (
    <article
      className={`bg-white rounded-xl shadow-md overflow-hidden group transition-all duration-300 hover:shadow-lg hover:transform hover:scale-[1.02] flex flex-col h-full ${
        featured ? 'ring-2 ring-brand-orange ring-opacity-50' : ''
      }`}
    >
      {/* Featured Badge */}
      {featured && (
        <div className="absolute top-3 left-3 z-10 bg-brand-orange text-white px-2 py-1 rounded-md text-xs font-medium">
          Featured
        </div>
      )}

      {/* Image */}
      <div className="relative overflow-hidden h-48">
        <img
          src={post.image}
          alt={post.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </div>

      {/* Content */}
      <div className="p-5 flex flex-col flex-grow">
        {/* Category and Read Time */}
        <div className="flex items-center justify-between mb-3">
          <span className="inline-block bg-brand-mint text-brand-teal text-xs font-medium px-2 py-1 rounded-md">
            {post.category}
          </span>
          <span className="text-brand-gray text-xs">
            {post.readTime} min read
          </span>
        </div>

        {/* Title */}
        <h3 className="text-lg font-bold text-brand-teal mb-2 font-poppins line-clamp-2">
          {post.title}
        </h3>

        {/* Excerpt */}
        <p className="text-brand-gray text-sm mb-4 line-clamp-2 flex-grow">
          {post.excerpt}
        </p>

        {/* Author and Date */}
        <div className="flex items-center justify-between mt-auto">
          <div className="flex items-center">
            <img
              src={post.author.avatar}
              alt={post.author.name}
              className="w-7 h-7 rounded-full mr-2"
            />
            <div>
              <p className="text-xs font-medium text-brand-teal">{post.author.name}</p>
              <p className="text-xs text-brand-gray">{formatDate(post.publishedDate)}</p>
            </div>
          </div>
          <button
            onClick={handleReadMore}
            className="bg-gradient-primary text-white px-3 py-1.5 rounded-md text-xs font-medium hover:shadow-teal transition-all duration-300"
            aria-label={`Read more about ${post.title}`}
          >
            Read More
          </button>
        </div>
      </div>
    </article>
  );
};

export default BlogCard;
