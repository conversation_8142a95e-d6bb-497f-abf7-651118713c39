
import React from 'react';
import { TESTIMONIALS_DATA } from '../constants';
import type { Testimonial } from '../types';
import { StarIcon } from './icons/StarIcon';
import { QuoteIcon } from './icons/QuoteIcon';

const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({ testimonial }) => {
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 !== 0;
    const starsArray = [];
    for (let i = 0; i < fullStars; i++) {
      starsArray.push(<StarIcon key={`full-${i}`} className="w-5 h-5 text-yellow-400" filled />);
    }
    if (halfStar) {
      // For simplicity, using full star for half star as well
      starsArray.push(<StarIcon key="half" className="w-5 h-5 text-yellow-400" filled />);
    }
    return starsArray;
  };

  return (
    <div className="bg-white p-8 rounded-xl shadow-xl flex flex-col items-center text-center transform hover:scale-105 transition-transform duration-300">
      <img
        src={testimonial.avatar}
        alt={testimonial.name}
        className="w-24 h-24 rounded-full object-cover mb-6 border-4 border-brand-orange"
      />
      <QuoteIcon className="w-8 h-8 text-brand-orange mb-4" />
      <p className="text-brand-gray font-montserrat italic mb-6 text-base leading-relaxed">
        "{testimonial.review}"
      </p>
      <div className="flex items-center mb-2">
        {renderStars(testimonial.rating)}
      </div>
      <h4 className="text-lg font-semibold text-brand-blue-dark font-poppins">{testimonial.name}</h4>
    </div>
  );
};

const Testimonials: React.FC = () => {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">What Our Customers Say</h2>
          <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
            Hear from fellow travelers who experienced the world with Rebel Rover.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {TESTIMONIALS_DATA.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
    