
import React from 'react';
import type { PackageDeal } from '../types';
import { StarIcon } from './icons/StarIcon';
import { LocationMarkerIconOutline } from './icons/HeroIcons';

interface PackageCardProps {
  packageInfo: PackageDeal;
  onViewDetails: (packageId: string) => void;
}

const PackageCard: React.FC<PackageCardProps> = ({ packageInfo, onViewDetails }) => {
  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 !== 0;
    const starsArray = [];
    for (let i = 0; i < fullStars; i++) {
      starsArray.push(<StarIcon key={`full-${i}`} className="w-5 h-5 text-yellow-400" filled />);
    }
    if (halfStar) {
      starsArray.push(<StarIcon key="half" className="w-5 h-5 text-yellow-400" filled />); 
    }
    return starsArray;
  };

  const handleViewDetailsClick = () => {
    if (typeof onViewDetails === 'function') {
      onViewDetails(packageInfo.id);
    } else {
      console.error(
        "PackageCard Error: onViewDetails prop is not a function. Received:", 
        onViewDetails,
        "Type:", typeof onViewDetails,
        "Package ID:", packageInfo.id
      );
      alert("Sorry, details for this package cannot be viewed at the moment.");
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300 flex flex-col h-full">
      <img src={packageInfo.image} alt={packageInfo.title} className="w-full h-56 object-cover" />
      <div className="p-6 flex flex-col flex-grow">
        <div className="flex items-center text-brand-gray text-sm mb-1">
          <LocationMarkerIconOutline className="w-4 h-4 mr-1 text-brand-orange" />
          {packageInfo.location}
        </div>
        <h3 className="text-xl font-semibold text-brand-blue-dark mb-2 font-poppins flex-grow">{packageInfo.title}</h3>
        <div className="flex items-center mb-3">
          {renderStars(packageInfo.rating)}
          <span className="ml-2 text-sm text-brand-gray">({packageInfo.rating.toFixed(1)})</span>
        </div>
        <p className="text-sm text-brand-gray mb-3">{packageInfo.duration}</p>
        <div className="flex justify-between items-center mt-auto">
          <p className="text-2xl font-bold text-brand-orange">${packageInfo.price}</p>
          <button 
            className="bg-brand-orange text-white px-6 py-2 rounded-lg text-sm font-semibold hover:bg-brand-orange-dark transition-colors"
            aria-label={`View details for ${packageInfo.title}`}
            onClick={handleViewDetailsClick}
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default PackageCard;
