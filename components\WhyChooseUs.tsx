
import React from 'react';
import { WHY_CHOOSE_US_FEATURES } from '../constants';
import type { FeatureInfo } from '../types';

// Enhanced Eco-Friendly Feature Card
const FeatureCard: React.FC<{ feature: FeatureInfo }> = ({ feature }) => {
  const Icon = feature.icon; // Assign component to a capitalized variable
  return (
    <div className="eco-card p-8 rounded-2xl shadow-eco-lg flex flex-col items-center text-center group relative overflow-hidden">
      {/* Eco-friendly background pattern */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-brand-mint/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-brand-green/10 rounded-full blur-lg"></div>

      <div className="relative z-10 p-5 bg-gradient-primary rounded-full text-white mb-6 inline-block shadow-eco group-hover:scale-110 transition-transform duration-300 eco-glow-hover">
        <Icon className="w-10 h-10" />
      </div>
      <h3 className="text-xl font-bold text-eco-primary mb-3 font-poppins group-hover:text-brand-teal transition-colors duration-300">{feature.title}</h3>
      <p className="text-brand-gray-dark text-sm font-montserrat leading-relaxed">{feature.description}</p>

      {/* Subtle eco accent */}
      <div className="absolute top-3 right-3 w-2 h-2 bg-brand-mint rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

const WhyChooseUs: React.FC = () => {
  return (
    <section id="about" className="py-16 lg:py-24 eco-section-light relative overflow-hidden">
      {/* Enhanced eco-friendly background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-40 h-40 bg-brand-teal rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-brand-mint rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-28 h-28 bg-brand-green rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-eco-primary mb-4 font-poppins">
            Why Choose Our Eco-Adventures?
          </h2>
          <p className="text-lg text-brand-gray-dark max-w-2xl mx-auto font-montserrat leading-relaxed">
            We offer sustainable, tailored travel experiences that protect our planet while ensuring every journey is unique, memorable, and environmentally responsible.
          </p>
        </div>
        {/* The type error for WHY_CHOOSE_US_FEATURES on this line (previously line 28)
            is resolved by correcting the FeatureInfo.icon type in types.ts */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {WHY_CHOOSE_US_FEATURES.map((feature, index) => (
            <FeatureCard key={index} feature={feature} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;