
import React from 'react';
import { WHY_CHOOSE_US_FEATURES } from '../constants';
import type { FeatureInfo } from '../types';

// Fixed: Assign feature.icon to a capitalized variable (Icon) to use it as a JSX component.
const FeatureCard: React.FC<{ feature: FeatureInfo }> = ({ feature }) => {
  const Icon = feature.icon; // Assign component to a capitalized variable
  return (
    <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-2xl transition-shadow duration-300 flex flex-col items-center text-center">
      <div className="p-4 bg-brand-orange rounded-full text-white mb-4 inline-block">
        <Icon className="w-8 h-8" />
      </div>
      <h3 className="text-xl font-semibold text-brand-blue-dark mb-2 font-poppins">{feature.title}</h3>
      <p className="text-brand-gray text-sm font-montserrat">{feature.description}</p>
    </div>
  );
};

const WhyChooseUs: React.FC = () => {
  return (
    <section id="about" className="py-16 lg:py-24 bg-brand-gray-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">Why Choose Us?</h2>
          <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
            We offer tailored travel experiences, ensuring every journey is unique and memorable.
          </p>
        </div>
        {/* The type error for WHY_CHOOSE_US_FEATURES on this line (previously line 28)
            is resolved by correcting the FeatureInfo.icon type in types.ts */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {WHY_CHOOSE_US_FEATURES.map((feature, index) => (
            <FeatureCard key={index} feature={feature} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;