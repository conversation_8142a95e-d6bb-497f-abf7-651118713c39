import React from 'react';
import { OUR_SERVICES_DATA } from '../constants';
import type { ServiceInfo } from '../types';

const ServiceCard: React.FC<{ service: ServiceInfo }> = ({ service }) => {
  const IconComponent = service.icon;
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden group transition-all duration-300 ease-in-out hover:shadow-2xl hover:scale-105 flex flex-col h-full">
      <div className="w-full h-48 overflow-hidden">
        <img 
          src={service.image} 
          alt={service.title} 
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
      </div>
      <div className="p-6 pt-0 flex flex-col items-center text-center flex-grow relative">
        <div className="p-3 bg-brand-orange rounded-full text-white mb-3 inline-block transform -translate-y-1/2 border-4 border-white group-hover:bg-brand-orange-dark transition-colors duration-300">
          <IconComponent className="w-6 h-6" />
        </div>
        <h3 className="text-xl font-semibold text-brand-blue-dark mb-2 font-poppins -mt-4">{service.title}</h3>
        <p className="text-brand-gray text-sm font-montserrat flex-grow">{service.description}</p>
      </div>
    </div>
  );
};

const OurServices: React.FC = () => {
  return (
    <section id="services" className="py-16 lg:py-24 bg-brand-gray-light">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-brand-blue-dark mb-3 font-poppins">Our Services</h2>
          <p className="text-lg text-brand-gray max-w-2xl mx-auto font-montserrat">
            Comprehensive travel solutions to make your journey seamless and enjoyable.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {OUR_SERVICES_DATA.map((service, index) => (
            <ServiceCard key={index} service={service} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurServices;