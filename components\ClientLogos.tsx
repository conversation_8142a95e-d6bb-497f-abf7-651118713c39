import React from 'react';
import { CLIENT_LOGOS_DATA } from '../constants';
import type { Client<PERSON>ogo } from '../types';

const ClientLogos: React.FC = () => {
  return (
    <section className="py-12 lg:py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10 lg:mb-12">
          <h2 className="text-2xl lg:text-3xl font-semibold text-brand-blue-dark mb-2 font-poppins">Trusted By</h2>
          <p className="text-md text-brand-gray max-w-xl mx-auto font-montserrat">
            We are proud to partner with leading companies and organizations worldwide.
          </p>
        </div>
        <div className="flex flex-wrap justify-center items-center gap-x-8 gap-y-6 md:gap-x-12 lg:gap-x-16">
          {CLIENT_LOGOS_DATA.map((logo: ClientLogo) => (
            <div key={logo.id} className="h-12 flex items-center">
              <img 
                src={logo.src} 
                alt={logo.alt} 
                className="max-h-full max-w-[120px] object-contain" 
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ClientLogos;
