
import React, { useState, useEffect } from 'react';
import { NAV_LINKS } from '../constants';
import type { NavLink } from '../types';

interface NavbarProps {
  onNavigate: (keyOrId: string, isPage?: boolean) => void;
  currentPage: string;
}

const Navbar: React.FC<NavbarProps> = ({ onNavigate, currentPage }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect for navbar background
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLinkClick = (link: NavLink) => {
    if (typeof onNavigate === 'function') {
      onNavigate(link.href, link.isPage);
    } else {
      console.error(
        'Navbar Error: onNavigate prop is not a function. Received:',
        onNavigate,
        'Type:', typeof onNavigate
      );
    }
    setIsMobileMenuOpen(false); // Close mobile menu on navigation
  };

  const isLinkActive = (link: NavLink) => {
    if (link.isPage) {
      return link.href === currentPage;
    }
    // Basic active state for anchor links if on home page and hash matches
    if (currentPage === 'home' && typeof window !== 'undefined' && window.location.hash === link.href) {
      return true;
    }
    return false;
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 py-4 lg:py-6 font-poppins transition-all duration-300 ${
      isScrolled || currentPage !== 'home'
        ? 'bg-brand-teal/95 backdrop-blur-sm shadow-teal'
        : 'bg-brand-teal/80 backdrop-blur-sm'
    }`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
        <a
          href="#home"
          onClick={(e) => {
            e.preventDefault();
            // Ensure the object passed to handleLinkClick matches NavLink structure
            handleLinkClick({ href: 'home', label: 'Home', isPage: true });
          }}
          className="text-3xl lg:text-4xl font-bold text-white cursor-pointer"
          aria-label="Go to homepage"
        >
          Rebel <span className="text-brand-green">Rover</span>
        </a>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex space-x-8 items-center">
          {NAV_LINKS.map((link: NavLink) => (
            <a
              key={link.label}
              href={link.isPage ? `javascript:void(0)` : link.href}
              onClick={(e) => {
                if (link.isPage || link.href.startsWith('#')) e.preventDefault();
                handleLinkClick(link);
              }}
              className={`text-white hover:text-brand-green transition-colors duration-300 text-lg ${
                isLinkActive(link) ? 'text-brand-green font-semibold' : ''
              }`}
              aria-current={isLinkActive(link) ? 'page' : undefined}
            >
              {link.label}
            </a>
          ))}
          <button
             onClick={() => alert('Book Eco-Adventure clicked!')}
            className="bg-gradient-secondary text-brand-teal px-8 py-3 rounded-lg font-semibold hover:shadow-eco-xl transition-all duration-300 text-lg shadow-eco transform hover:scale-105">
            Book Eco-Adventure
          </button>
        </div>

        {/* Mobile Menu Button */}
        <div className="lg:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-white focus:outline-none p-2"
            aria-label="Toggle mobile menu"
            aria-expanded={isMobileMenuOpen}
            aria-controls="mobile-menu"
          >
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                  aria-hidden="true"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16m-7 6h7"
                  aria-hidden="true"
                />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div
          id="mobile-menu"
          className="lg:hidden absolute top-full left-0 right-0 bg-brand-teal-dark/95 backdrop-blur-sm shadow-teal mt-1"
        >
          <div className="container mx-auto px-4 sm:px-6 py-4 flex flex-col space-y-4 items-center">
            {NAV_LINKS.map((link: NavLink) => (
              <a
                key={link.label}
                href={link.isPage ? `javascript:void(0)` : link.href}
                onClick={(e) => {
                  if (link.isPage || link.href.startsWith('#')) e.preventDefault();
                  handleLinkClick(link);
                }}
                className={`text-white hover:text-brand-green transition-colors duration-300 text-lg py-2 ${
                  isLinkActive(link) ? 'text-brand-green font-semibold' : ''
                }`}
                aria-current={isLinkActive(link) ? 'page' : undefined}
              >
                {link.label}
              </a>
            ))}
            <button
              onClick={() => alert('Book Eco-Adventure clicked!')}
              className="bg-gradient-secondary text-brand-teal px-8 py-3 rounded-lg font-semibold hover:shadow-eco-xl transition-all duration-300 text-lg w-full max-w-xs shadow-eco">
              Book Eco-Adventure
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
